/**
 * 图片转换相关类型定义
 */

/**
 * 转换进度回调函数类型
 */
export type ProgressCallback = (current: number, total: number, fileName: string) => void

/**
 * 文件转换结果类型
 */
export interface ConversionResult {
  blob: Blob
  fileName: string
}

/**
 * Content Script 转换结果类型
 */
export interface ContentScriptConversionResult {
  dataUrl: string
  fileName: string
}

/**
 * 格式选项类型
 */
export interface FormatOption {
  value: string
  label: string
}

/**
 * 用户设置类型
 */
export interface UserSettings {
  outputFormat: string
  quality: number
}
