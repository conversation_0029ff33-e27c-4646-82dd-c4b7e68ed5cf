import { useState, useRef, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { getUserSettings, saveOutputFormat } from "./utils/storage"
import { convertMultipleFiles, convertMultipleFilesToZip } from "./utils/popup-converter"
import { OUTPUT_FORMAT_OPTIONS } from "./constants/converter"
import "./i18n"
import "./style.css"

function IndexPopup() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [outputFormat, setOutputFormat] = useState("JPG")
  const [isDragOver, setIsDragOver] = useState(false)
  const [isConverting, setIsConverting] = useState(false)
  const [convertProgress, setConvertProgress] = useState({ current: 0, total: 0, fileName: '' })
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { t } = useTranslation()

  // 初始化时加载用户设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await getUserSettings()
        setOutputFormat(settings.outputFormat)
      } catch (error) {
        console.error('加载用户设置失败:', error)
      }
    }
    loadSettings()
  }, [])

  // 格式变化时保存设置并通知 background
  const handleFormatChange = async (newFormat: string) => {
    setOutputFormat(newFormat)

    try {
      // 保存到存储
      await saveOutputFormat(newFormat)

      // 通知 background script 更新右键菜单
      chrome.runtime.sendMessage({
        action: "updateContextMenu",
        format: newFormat
      })

      console.log('格式已更新为:', newFormat)
    } catch (error) {
      console.error('更新格式失败:', error)
    }
  }

  const handleFilesSelect = async (files: FileList | File[]) => {
    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))
    if (imageFiles.length > 0) {
      setSelectedFiles(imageFiles)
      // 立即开始转换和下载
      await convertAndDownloadFiles(imageFiles)
    }
  }

  const convertAndDownloadFiles = async (files: File[]) => {
    if (isConverting) return

    setIsConverting(true)
    setConvertProgress({ current: 0, total: files.length, fileName: '' })

    try {
      if (files.length > 1) {
        // 多个文件时自动打包成压缩包
        await convertMultipleFilesToZip(
          files,
          outputFormat,
          (current, total, fileName) => {
            setConvertProgress({ current, total, fileName })
          }
        )
        console.log('所有文件转换完成并已打包')
      } else {
        // 单个文件时单独下载
        await convertMultipleFiles(
          files,
          outputFormat,
          (current, total, fileName) => {
            setConvertProgress({ current, total, fileName })
          }
        )
        console.log('文件转换完成')
      }
    } catch (error) {
      console.error('批量转换失败:', error)
    } finally {
      setIsConverting(false)
      setConvertProgress({ current: 0, total: 0, fileName: '' })
      // 清空选择的文件
      setSelectedFiles([])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    if (isConverting) return
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFilesSelect(files)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (isConverting) return
    const files = e.target.files
    if (files && files.length > 0) {
      handleFilesSelect(files)
    }
  }

  const handleClickUpload = () => {
    if (isConverting) return
    fileInputRef.current?.click()
  }

  return (
    <div className="flex flex-col items-start relative w-[360px] h-[302px] bg-white p-4 gap-2.5 font-inter">
      {/* Header Frame */}
      <div className="flex flex-row items-center justify-between p-0 gap-2.5 w-[328px] h-6 flex-none order-0 self-stretch">
        <h1 className="font-medium text-base leading-[150%] text-[#000000] flex-grow truncate">
          {t('common.title')}
        </h1>
        {/* <button
          className="font-medium text-sm leading-[150%] text-[#1A56DB] hover:text-blue-700 hover:underline transition-colors whitespace-nowrap flex-shrink-0"
          onClick={() => window.open('https://example.com', '_blank')}
        >
          {t('common.visitWebsite')}
        </button> */}
      </div>

      {/* Upload Area */}
      <div
        className={`flex flex-col justify-center items-center p-8 gap-2.5 w-[328px] h-40 ${
          isDragOver ? 'bg-[#F9FAFB]' : 'bg-[#F9FAFB]'
        } border-2 border-dashed ${
          isDragOver ? 'border-[#C3DDFD]' : 'border-gray-200'
        } rounded-lg ${isConverting ? 'cursor-not-allowed opacity-60' : 'cursor-pointer hover:bg-[#F9FAFB] hover:border-[#C3DDFD]'} transition-all duration-200 flex-none order-1 self-stretch group`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClickUpload}
      >
        {/* Drop files content */}
        <div className="flex flex-col justify-center items-center p-0 gap-3 w-full max-w-[280px] min-h-[49px] flex-none order-0">
          {/* Image Icon */}
          <div className="w-5 h-5 flex-none order-0 relative">
            <svg
              width="20"
              height="19"
              viewBox="0 0 20 19"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="transition-colors duration-200"
            >
              <path
                d="M18 0.333328H2C1.46957 0.333328 0.960859 0.547944 0.585786 0.929963C0.210714 1.31198 0 1.83011 0 2.37037V16.6296C0 17.1699 0.210714 17.688 0.585786 18.07C0.960859 18.452 1.46957 18.6667 2 18.6667H18C18.5304 18.6667 19.0391 18.452 19.4142 18.07C19.7893 17.688 20 17.1699 20 16.6296V2.37037C20 1.83011 19.7893 1.31198 19.4142 0.929963C19.0391 0.547944 18.5304 0.333328 18 0.333328ZM12.5 4.4074C12.7967 4.4074 13.0867 4.49701 13.3334 4.66488C13.58 4.83275 13.7723 5.07136 13.8858 5.35052C13.9994 5.62969 14.0291 5.93688 13.9712 6.23324C13.9133 6.5296 13.7704 6.80182 13.5607 7.01548C13.3509 7.22915 13.0836 7.37465 12.7926 7.4336C12.5017 7.49255 12.2001 7.4623 11.926 7.34666C11.6519 7.23103 11.4176 7.03521 11.2528 6.78397C11.088 6.53273 11 6.23735 11 5.93518C11 5.52999 11.158 5.14139 11.4393 4.85488C11.7206 4.56836 12.1022 4.4074 12.5 4.4074ZM16.876 15.0825C16.7898 15.2424 16.6631 15.3759 16.509 15.4689C16.3549 15.5618 16.1791 15.611 16 15.6111H4C3.82952 15.6112 3.66186 15.5669 3.51293 15.4824C3.364 15.3979 3.23874 15.276 3.14907 15.1284C3.05939 14.9807 3.00827 14.8121 3.00056 14.6387C2.99285 14.4652 3.0288 14.2926 3.105 14.1373L6.605 7.00768C6.68557 6.84323 6.80833 6.70404 6.96026 6.60488C7.1122 6.50572 7.28764 6.45028 7.468 6.44444C7.64992 6.43413 7.83098 6.47649 7.99025 6.56661C8.14952 6.65673 8.28049 6.79094 8.368 6.9537L11.143 11.7988L12.689 9.87685C12.7894 9.75228 12.9174 9.65376 13.0625 9.58939C13.2076 9.52502 13.3656 9.49664 13.5236 9.50658C13.6815 9.51652 13.8349 9.56451 13.9711 9.64658C14.1073 9.72865 14.2224 9.84248 14.307 9.9787L16.848 14.0528C16.9421 14.2064 16.9943 14.3828 16.9992 14.5638C17.0042 14.7448 16.9616 14.9238 16.876 15.0825Z"
                fill="#6B7280"
                className="group-hover:fill-[#1A56DB]"
              />
            </svg>
          </div>

          {/* Text & Link */}
          <div className="flex flex-col items-center justify-center p-0 gap-1 w-full flex-none order-1">
            <p className="font-normal text-sm leading-[150%] text-center text-gray-500 group-hover:text-[#1A56DB] break-words px-4 w-full transition-colors duration-200">
              {isConverting ? (
                <span className="font-medium text-blue-600 block">
                  {t('popup.converting', { current: convertProgress.current, total: convertProgress.total })}
                  {convertProgress.fileName && (
                    <span className="block text-xs text-gray-500 mt-1 truncate">
                      {convertProgress.fileName}
                    </span>
                  )}
                </span>
              ) : selectedFiles.length > 0 ? (
                <span className="font-medium text-green-600 block">
                  {t('popup.filesSelected', { count: selectedFiles.length })}
                  {selectedFiles.length === 1 && (
                    <span className="block text-xs text-gray-500 mt-1 truncate">
                      {selectedFiles[0].name}
                    </span>
                  )}
                </span>
              ) : (
                <span>
                  {t('popup.dragDropText')}
                </span>
              )}
            </p>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
        />
      </div>

      {/* Select Input */}
      <div className="flex flex-col items-start p-0 gap-2 w-[328px] min-h-[66px] flex-none order-2 self-stretch">
        {/* Label */}
        <div className="flex flex-row items-center p-0 gap-2 w-[328px] h-[21px] flex-none order-0 self-stretch">
          <label className="font-medium text-sm leading-[150%] text-[#111928] flex-none order-0">
            {t('popup.outputFormat')}
          </label>
        </div>

        {/* Input */}
        <div className="relative w-[328px] h-[37px] flex-none order-1 self-stretch">
          <select
            value={outputFormat}
            onChange={(e) => handleFormatChange(e.target.value)}
            className="w-full h-full py-2 px-3 pr-10 font-normal text-sm leading-[150%] text-[#111928] bg-white border border-[#D1D5DB] rounded-lg cursor-pointer outline-none hover:border-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-all duration-200 appearance-none"
          >
            {OUTPUT_FORMAT_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          {/* Chevron down icon */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 w-2.5 h-2.5 pointer-events-none">
            <svg
              width="10"
              height="10"
              viewBox="0 0 24 24"
              fill="none"
              className="text-gray-500"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  )
}

export default IndexPopup
