import { initializeImageMagick, ImageMagick, MagickFormat } from '@imagemagick/magick-wasm'

// WASM 初始化状态
let isInitialized = false
let initializationPromise: Promise<void> | null = null

// 转换互斥锁，确保一次只转换一个文件
let conversionMutex: Promise<any> = Promise.resolve()

/**
 * 初始化 ImageMagick WASM
 * 使用本地内置的 WASM 文件
 */
export async function initializeMagick(): Promise<void> {
  if (isInitialized) {
    return
  }

  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = (async () => {
    try {
      console.log('开始初始化 ImageMagick WASM...')

      // 在 Chrome 扩展中，尝试不同的初始化方法
      try {
        // 方法1：使用预加载的 WASM 文件
        const wasmUrl = chrome.runtime.getURL('node_modules/@imagemagick/magick-wasm/dist/magick.wasm')
        console.log('WASM URL:', wasmUrl)

        const response = await fetch(wasmUrl)
        if (!response.ok) {
          throw new Error(`加载 WASM 文件失败: ${response.status}`)
        }

        const wasmBytes = await response.arrayBuffer()
        console.log('WASM 文件大小:', wasmBytes.byteLength)

        // 直接使用 ImageMagick 的初始化方法
        await initializeImageMagick(wasmBytes)
        console.log('使用预加载 WASM 初始化成功')

      } catch (wasmError) {
        console.warn('预加载 WASM 方法失败，尝试URL方式:', wasmError)
        // 方法2：使用 URL 方式让 ImageMagick 自己加载 WASM
        const wasmUrl = new URL(chrome.runtime.getURL('node_modules/@imagemagick/magick-wasm/dist/magick.wasm'))
        await initializeImageMagick(wasmUrl)
        console.log('使用URL方式 WASM 初始化成功')
      }

      isInitialized = true
      console.log('✅ ImageMagick WASM 初始化成功')
    } catch (error) {
      console.error('❌ ImageMagick WASM 初始化失败:', error)
      initializationPromise = null
      throw error
    }
  })()

  return initializationPromise
}

/**
 * 检查是否已初始化
 */
export function isMagickInitialized(): boolean {
  return isInitialized
}

/**
 * 使用 ImageMagick 转换图片格式
 */
export async function convertImageWithMagick(
  imageData: ArrayBuffer,
  targetFormat: MagickFormat,
  quality: number = 90
): Promise<Uint8Array> {
  if (!isInitialized) {
    await initializeMagick()
  }

  // 使用互斥锁确保一次只转换一个文件
  const currentConversion = conversionMutex.then(async () => {
    try {
      console.log('🔒 获取转换锁，开始转换...')

      const result = await new Promise<Uint8Array>((innerResolve, innerReject) => {
        try {
          console.log('📖 ImageMagick 开始读取图片数据，大小:', imageData.byteLength)

          ImageMagick.read(new Uint8Array(imageData), (image) => {
            try {
              console.log('✅ ImageMagick 读取成功')
              console.log('📋 图片信息: 宽度=', image.width, '高度=', image.height)

              // 设置图片质量
              if (targetFormat === MagickFormat.Jpeg) {
                image.quality = quality
                console.log('🎛️ 设置 JPEG 质量:', quality)
              }

              console.log('🔄 开始写入目标格式:', targetFormat)

              // 转换格式并输出
              image.write(targetFormat, (data) => {
                try {
                  console.log('✅ ImageMagick 写入完成，输出数据大小:', data.length)

                  // 验证输出数据
                  if (!data || data.length === 0) {
                    innerReject(new Error('ImageMagick 输出数据为空'))
                    return
                  }

                  // 立即深拷贝数据，避免内存被重用导致数据损坏
                  const copiedData = new Uint8Array(data.length)
                  copiedData.set(data)
                  console.log('📋 数据已深拷贝，大小:', copiedData.length)

                  // 确保图片对象被正确释放
                  image.dispose()
                  console.log('🗑️ ImageMagick 图片对象已释放')

                  innerResolve(copiedData)
                } catch (disposeError) {
                  console.error('❌ ImageMagick 释放失败:', disposeError)
                  // 即使释放失败也要深拷贝数据
                  const copiedData = new Uint8Array(data.length)
                  copiedData.set(data)
                  innerResolve(copiedData)
                }
              })
            } catch (writeError) {
              console.error('❌ ImageMagick write 失败:', writeError)
              try {
                image.dispose()
              } catch (disposeError) {
                console.error('❌ 清理失败:', disposeError)
              }
              innerReject(writeError)
            }
          })
        } catch (readError) {
          console.error('❌ ImageMagick read 失败:', readError)
          innerReject(readError)
        }
      })

      console.log('🔓 释放转换锁')
      return result
    } catch (error) {
      console.error('❌ 转换过程失败:', error)
      throw error
    }
  })

  // 更新互斥锁
  conversionMutex = currentConversion.catch(() => {
    // 即使当前转换失败，也要继续处理下一个
  })

  return currentConversion
}

/**
 * 获取支持的图片格式
 */
export function getSupportedFormats(): string[] {
  return [
    'JPEG', 'JPG', 'PNG', 'GIF', 'WEBP', 'BMP', 'TIFF', 'TIF',
    'ICO', 'SVG', 'AVIF', 'HEIC', 'HEIF'
  ]
}

/**
 * 根据文件扩展名获取 MagickFormat
 */
export function getMagickFormat(extension: string): MagickFormat {
  const ext = extension.toLowerCase().replace('.', '')
  
  switch (ext) {
    case 'jpg':
    case 'jpeg':
      return MagickFormat.Jpeg
    case 'png':
      return MagickFormat.Png
    case 'gif':
      return MagickFormat.Gif
    case 'webp':
      return MagickFormat.WebP
    case 'bmp':
      return MagickFormat.Bmp
    case 'tiff':
    case 'tif':
      return MagickFormat.Tiff
    case 'ico':
      return MagickFormat.Ico
    case 'svg':
      return MagickFormat.Svg
    case 'avif':
      return MagickFormat.Avif
    default:
      return MagickFormat.Jpeg // 默认转换为 JPEG
  }
}
