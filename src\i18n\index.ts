import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

import en from './locales/en.json'
import zh from './locales/zh.json'
import zh_TW from './locales/zh_TW.json'
import hi from './locales/hi.json'
import es from './locales/es.json'
import fr from './locales/fr.json'
import ru from './locales/ru.json'
import id from './locales/id.json'
import bn from './locales/bn.json'
import pt from './locales/pt.json'
import de from './locales/de.json'
import ja from './locales/ja.json'

// 语言映射表
const languageMap: Record<string, any> = {
  'en': en,
  'zh': zh,
  'zh-CN': zh,
  'zh_CN': zh,
  'zh-TW': zh_TW,
  'zh_TW': zh_TW,
  'hi': hi,
  'es': es,
  'fr': fr,
  'ru': ru,
  'id': id,
  'bn': bn,
  'pt': pt,
  'de': de,
  'ja': ja
}

// 为 background script 提供的简单多语言函数
export function getBackgroundText(key: string): string {
  const currentLanguage = chrome.i18n.getUILanguage()

  // 尝试精确匹配
  let translations = languageMap[currentLanguage]

  // 如果没有精确匹配，尝试语言前缀匹配
  if (!translations) {
    const languagePrefix = currentLanguage.split('-')[0]
    translations = languageMap[languagePrefix]
  }

  // 如果还是没有，回退到英语
  if (!translations) {
    translations = en
  }

  // 支持嵌套键，如 'contextMenu.saveAsFormat'
  const keys = key.split('.')
  let result: any = translations

  for (const k of keys) {
    result = result?.[k]
    if (result === undefined) break
  }

  return result || key
}

const resources = {
  en: { translation: en },
  zh: { translation: zh },
  zh_TW: { translation: zh_TW },
  hi: { translation: hi },
  es: { translation: es },
  fr: { translation: fr },
  ru: { translation: ru },
  id: { translation: id },
  bn: { translation: bn },
  pt: { translation: pt },
  de: { translation: de },
  ja: { translation: ja }
}

// 检测浏览器语言
function detectLanguage(): string {
  try {
    const chromeLanguage = chrome?.i18n?.getUILanguage?.()
    if (chromeLanguage) {
      // 标准化语言代码
      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')

      // 检查是否支持该语言
      if (resources[normalizedLang]) {
        return normalizedLang
      }

      // 尝试语言前缀匹配
      const languagePrefix = normalizedLang.split('_')[0]
      if (resources[languagePrefix]) {
        return languagePrefix
      }
    }
  } catch (error) {
    console.log('Chrome i18n API不可用，使用navigator.language')
  }

  const browserLanguage = navigator.language || navigator.languages?.[0] || 'en'
  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')

  // 检查是否支持该语言
  if (resources[normalizedBrowserLang]) {
    return normalizedBrowserLang
  }

  // 尝试语言前缀匹配
  const languagePrefix = normalizedBrowserLang.split('_')[0]
  if (resources[languagePrefix]) {
    return languagePrefix
  }

  // 回退到英语
  return 'en'
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: detectLanguage(),
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false
    }
  })

export default i18n
