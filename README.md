# Image Download Extension

这是一个使用 [Plasmo](https://docs.plasmo.com/) 框架构建的浏览器扩展项目，集成了 TypeScript、React、TailwindCSS 和 i18n 国际化支持。

## 技术栈

- **框架**: Plasmo
- **语言**: TypeScript
- **UI库**: React 18
- **样式**: TailwindCSS
- **国际化**: react-i18next
- **包管理器**: pnpm

## 项目结构

```
src/
├── constants/          # 常量定义
├── contents/           # Content Scripts
├── i18n/               # 国际化配置
│   ├── index.ts        # i18n 配置文件
│   └── locales/        # 语言文件
│       ├── en.json     # 英文
│       └── zh.json     # 中文
├── types/              # 类型定义
├── utils/              # 工具函数
├── background.ts       # 后台脚本
├── popup.tsx          # 弹出页面
└── style.css          # 全局样式
```

## 开发指南

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 打包扩展

```bash
pnpm package
```

## 功能特性

### 🌍 国际化支持
- 支持中英文切换
- 自动检测浏览器语言
- 语言设置本地存储

### 🎨 现代化UI
- 使用 TailwindCSS 构建响应式界面
- 统一的设计系统
- 优雅的交互动画

### 📱 扩展功能
- **Popup**: 扩展弹出窗口，支持图片格式转换
- **Context Menu**: 右键菜单图片转换
- **Background Script**: 后台处理和存储管理

## 配置说明

### TailwindCSS 配置
项目已配置 TailwindCSS，配置文件位于 `tailwind.config.js`。

### i18n 配置
国际化配置位于 `src/i18n/index.ts`，支持：
- 语言自动检测
- 本地存储
- 动态语言切换

### 扩展权限
在 `package.json` 中配置了必要的扩展权限：
- `tabs`: 标签页访问权限
- `host_permissions`: 网站访问权限

## 开发注意事项

1. **Node.js 版本**: 建议使用 Node.js 18.17.0+ 或 20.3.0+
2. **包管理器**: 推荐使用 pnpm
3. **热重载**: 开发模式下支持热重载
4. **类型检查**: 项目使用 TypeScript 进行类型检查

## 部署

1. 运行 `pnpm build` 构建生产版本
2. 生成的文件位于 `build/` 目录
3. 将对应的构建文件夹（如 `build/chrome-mv3-prod`）打包上传到浏览器扩展商店

