import type { PlasmoCSConfig } from "plasmo"
import { initializeMagick, convertImageWithMagick, getMagickFormat } from "../utils/magick-loader"
import type { ContentScriptConversionResult } from "../types/converter"
import { DEFAULT_JPEG_QUALITY, MIME_TYPE_MAP, DEFAULT_MIME_TYPE } from "../constants/converter"

export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"],
  all_frames: true
}

// 监听来自 background script 的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // 只在主框架中处理消息，避免iframe中的重复处理
  if (window !== window.top) {
    console.log('⚠️ 非主框架，跳过消息处理')
    return false
  }

  if (request.action === "convertImageToJpg") {
    console.log('🔄 Content Script开始处理convertImageToJpg消息')
    convertImageToJpg(request.imageUrl)
      .then(result => sendResponse({ success: true, ...result }))
      .catch(error => sendResponse({ success: false, error: error.message }))
    return true // 保持消息通道开放以支持异步响应
  } else if (request.action === "convertImageToFormat") {
    console.log('🔄 Content Script开始处理convertImageToFormat消息')
    convertImageToFormat(request.imageUrl, request.targetFormat)
      .then(result => sendResponse({ success: true, ...result }))
      .catch(error => sendResponse({ success: false, error: error.message }))
    return true // 保持消息通道开放以支持异步响应
  } else if (request.action === "downloadWithDOM") {
    // 使用传统DOM方法下载
    console.log('🔄 Content Script开始处理downloadWithDOM消息')
    try {
      downloadWithTraditionalMethod(request.dataUrl, request.fileName)
      sendResponse({ success: true })
    } catch (error) {
      console.error('DOM下载失败:', error)
      sendResponse({ success: false, error: error.message })
    }
    return true
  }
})

// 将图片转换为 JPG 格式（使用 ImageMagick）
async function convertImageToJpg(imageUrl: string): Promise<ContentScriptConversionResult> {
  try {
    console.log('🔄 开始转换图片为JPG:', imageUrl)

    // 初始化 ImageMagick
    console.log('📦 初始化 ImageMagick...')
    await initializeMagick()
    console.log('✅ ImageMagick 初始化完成')

    // 获取图片数据
    console.log('📖 获取图片数据...')
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`获取图片失败: ${response.status}`)
    }

    const imageArrayBuffer = await response.arrayBuffer()
    console.log('✅ 图片数据获取完成，大小:', imageArrayBuffer.byteLength)

    // 使用 ImageMagick 转换为 JPEG
    console.log('🎨 开始 ImageMagick 转换为JPG...')
    const convertedData = await convertImageWithMagick(
      imageArrayBuffer,
      getMagickFormat('jpg'),
      DEFAULT_JPEG_QUALITY // JPEG 质量
    )

    // 验证转换数据
    if (!convertedData || convertedData.length === 0) {
      throw new Error('ImageMagick 转换返回空数据')
    }

    console.log('✅ ImageMagick 转换完成，数据长度:', convertedData.length)

    // 转换为 data URL
    console.log('📦 创建 Blob...')
    const mimeType = getMimeType('JPG')
    const blob = new Blob([convertedData], { type: mimeType })
    const dataUrl = await blobToDataUrl(blob)

    // 生成文件名
    const fileName = generateFileName(imageUrl, 'JPG')

    console.log('✅ 图片转换成功！输出大小:', convertedData.length, '文件名:', fileName)
    return { dataUrl, fileName }

  } catch (error) {
    console.error('❌ ImageMagick 转换失败:', error)
    throw error
  }
}

// 将图片转换为指定格式（使用 ImageMagick）
async function convertImageToFormat(imageUrl: string, targetFormat: string): Promise<ContentScriptConversionResult> {
  try {
    console.log('🔄 开始转换图片:', imageUrl, '目标格式:', targetFormat)

    // 初始化 ImageMagick
    console.log('📦 初始化 ImageMagick...')
    await initializeMagick()
    console.log('✅ ImageMagick 初始化完成')

    // 获取图片数据
    console.log('📖 获取图片数据...')
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`获取图片失败: ${response.status}`)
    }

    const imageArrayBuffer = await response.arrayBuffer()
    console.log('✅ 图片数据获取完成，大小:', imageArrayBuffer.byteLength)

    // 使用 ImageMagick 转换为目标格式
    console.log('🎨 开始 ImageMagick 转换...')
    const magickFormat = getMagickFormat(targetFormat.toLowerCase())
    console.log('📋 目标格式:', magickFormat)
    const quality = targetFormat.toUpperCase() === 'JPG' || targetFormat.toUpperCase() === 'JPEG' ? DEFAULT_JPEG_QUALITY : undefined

    const convertedData = await convertImageWithMagick(
      imageArrayBuffer,
      magickFormat,
      quality
    )

    // 验证转换数据
    if (!convertedData || convertedData.length === 0) {
      throw new Error('ImageMagick 转换返回空数据')
    }

    console.log('✅ ImageMagick 转换完成，数据长度:', convertedData.length)

    // 验证WebP文件头（如果是WebP格式）
    if (targetFormat.toUpperCase() === 'WEBP') {
      const header = new Uint8Array(convertedData.slice(0, 12))
      const riffSignature = String.fromCharCode(...header.slice(0, 4))
      const webpSignature = String.fromCharCode(...header.slice(8, 12))

      console.log('🔍 WebP 文件头检查:')
      console.log('  原始字节数据:', Array.from(header.slice(0, 4)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
      console.log('  RIFF 签名:', JSON.stringify(riffSignature), '(应该是 "RIFF")')
      console.log('  WEBP 字节数据:', Array.from(header.slice(8, 12)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
      console.log('  WEBP 签名:', JSON.stringify(webpSignature), '(应该是 "WEBP")')

      if (riffSignature !== 'RIFF' || webpSignature !== 'WEBP') {
        console.error('❌ WebP 文件头无效')
        console.error('  完整的前12字节:', Array.from(header).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
        throw new Error('WebP 文件头无效')
      }
    }

    // 转换为 data URL
    console.log('📦 创建 Blob...')
    const mimeType = getMimeType(targetFormat)
    const blob = new Blob([convertedData], { type: mimeType })
    const dataUrl = await blobToDataUrl(blob)

    // 生成文件名
    const fileName = generateFileName(imageUrl, targetFormat)

    console.log('✅ 图片转换成功！输出大小:', convertedData.length, '文件名:', fileName)
    return { dataUrl, fileName }

  } catch (error) {
    console.error('ImageMagick 转换失败:', error)
    throw error
  }
}

// 将 Blob 转换为 Data URL
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

// 获取 MIME 类型
function getMimeType(format: string): string {
  const formatUpper = format.toUpperCase()
  return MIME_TYPE_MAP[formatUpper] || DEFAULT_MIME_TYPE
}

// 生成文件名
function generateFileName(imageUrl: string, format: string = 'JPG'): string {
  try {
    const url = new URL(imageUrl)
    const pathname = url.pathname
    const pathSegments = pathname.split('/').filter(segment => segment.length > 0)
    const lastSegment = pathSegments[pathSegments.length - 1] || ''

    let fileName = ''

    // 尝试从pathname中提取文件名
    if (lastSegment && /\.[a-zA-Z0-9]+$/.test(lastSegment)) {
      fileName = lastSegment
    } else {
      // 如果没有文件名，从hostname生成
      const hostname = url.hostname.replace(/^www\./, '')
      const timestamp = Date.now().toString().slice(-6)
      fileName = `${hostname}_image_${timestamp}`
    }

    // 移除原有扩展名并添加新的扩展名
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '')
    const extension = format.toLowerCase()
    const finalFileName = `${nameWithoutExt}.${extension}`

    console.log('生成文件名:', finalFileName, '来源URL:', imageUrl)
    return finalFileName

  } catch (error) {
    console.error('生成文件名失败:', error)
    const extension = format.toLowerCase()
    const fallbackName = `image_${Date.now()}.${extension}`
    return fallbackName
  }
}

// 使用传统DOM方法下载（参考popup-converter.ts的实现）
function downloadWithTraditionalMethod(dataUrl: string, fileName: string): void {
  try {
    console.log('🔄 Content Script使用传统下载方法:', fileName)
    
    // 创建临时链接元素
    const link = document.createElement('a')
    link.href = dataUrl
    link.download = fileName
    link.style.display = 'none'
    
    // 确保元素被添加到DOM中
    document.body.appendChild(link)
    
    // 触发下载
    link.click()
    
    // 立即清理DOM元素
    document.body.removeChild(link)
    
    console.log('✅ Content Script传统下载触发成功:', fileName)
  } catch (error) {
    console.error('❌ Content Script传统下载失败:', error)
    throw error
  }
}


