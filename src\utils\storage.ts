/**
 * Chrome 扩展存储工具
 * 用于保存和获取用户设置
 */

import type { UserSettings } from "../types/converter"
import { DEFAULT_USER_SETTINGS } from "../constants/converter"

/**
 * 获取用户设置
 */
export async function getUserSettings(): Promise<UserSettings> {
  try {
    const result = await chrome.storage.sync.get(DEFAULT_USER_SETTINGS)
    return result as UserSettings
  } catch (error) {
    console.error('获取用户设置失败:', error)
    return DEFAULT_USER_SETTINGS
  }
}

/**
 * 保存用户设置
 */
export async function saveUserSettings(settings: Partial<UserSettings>): Promise<void> {
  try {
    await chrome.storage.sync.set(settings)
    console.log('用户设置已保存:', settings)
  } catch (error) {
    console.error('保存用户设置失败:', error)
  }
}

/**
 * 获取输出格式
 */
export async function getOutputFormat(): Promise<string> {
  const settings = await getUserSettings()
  return settings.outputFormat
}

/**
 * 保存输出格式
 */
export async function saveOutputFormat(format: string): Promise<void> {
  await saveUserSettings({ outputFormat: format })
}
