/**
 * 图片转换相关常量定义
 */

import type { FormatOption } from "../types/converter"

/**
 * 默认JPEG质量
 */
export const DEFAULT_JPEG_QUALITY = 90

/**
 * 转换延迟时间（毫秒）
 */
export const CONVERSION_DELAY = 200

/**
 * 下载完成等待时间（毫秒）
 */
export const DOWNLOAD_WAIT_TIME = 800

/**
 * URL清理延迟时间（毫秒）
 */
export const URL_CLEANUP_DELAY = 1000

/**
 * 压缩级别
 */
export const ZIP_COMPRESSION_LEVEL = 6

/**
 * MIME类型映射
 */
export const MIME_TYPE_MAP: Record<string, string> = {
  'JPG': 'image/jpeg',
  'JPEG': 'image/jpeg',
  'PNG': 'image/png',
  'WEBP': 'image/webp',
  'GIF': 'image/gif',
  'BMP': 'image/bmp',
  'TIFF': 'image/tiff',
  'TIF': 'image/tiff',
  'ICO': 'image/x-icon',
  'SVG': 'image/svg+xml',
  'AVIF': 'image/avif'
}

/**
 * 默认MIME类型
 */
export const DEFAULT_MIME_TYPE = 'image/jpeg'

/**
 * 支持的输出格式选项
 */
export const OUTPUT_FORMAT_OPTIONS: readonly FormatOption[] = [
  { value: 'JPG', label: 'JPG' },
  { value: 'PNG', label: 'PNG' },
  { value: 'WEBP', label: 'WEBP' },
  { value: 'BMP', label: 'BMP' },
  { value: 'GIF', label: 'GIF' },
  { value: 'TIFF', label: 'TIFF' },
  { value: 'ICO', label: 'ICO' }
] as const

/**
 * 默认用户设置
 */
export const DEFAULT_USER_SETTINGS = {
  outputFormat: 'JPG',
  quality: 90,
}

