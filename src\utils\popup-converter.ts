import { initializeMagick, convertImageWithMagick, getMagickFormat } from "./magick-loader"
import J<PERSON><PERSON><PERSON> from "jszip"
import type { ProgressCallback, ConversionResult } from "../types/converter"
import {
  DEFAULT_JPEG_QUALITY,
  CONVERSION_DELAY,
  DOWNLOAD_WAIT_TIME,
  URL_CLEANUP_DELAY,
  ZIP_COMPRESSION_LEVEL,
  MIME_TYPE_MAP,
  DEFAULT_MIME_TYPE
} from "../constants/converter"

/**
 * 在 popup 中直接转换图片文件
 */
export async function convertFileToFormat(
  file: File,
  targetFormat: string
): Promise<ConversionResult> {
  try {
    console.log('🔄 开始转换文件:', file.name, '目标格式:', targetFormat, '文件大小:', file.size)

    // 初始化 ImageMagick
    console.log('📦 初始化 ImageMagick...')
    await initializeMagick()
    console.log('✅ ImageMagick 初始化完成')

    // 读取文件为 ArrayBuffer
    console.log('📖 读取文件数据...')
    const fileArrayBuffer = await file.arrayBuffer()
    console.log('✅ 文件数据读取完成，大小:', fileArrayBuffer.byteLength)

    // 使用 ImageMagick 转换
    console.log('🎨 开始 ImageMagick 转换...')
    const magickFormat = getMagickFormat(targetFormat.toLowerCase())
    console.log('📋 目标格式:', magickFormat)
    const quality = targetFormat.toUpperCase() === 'JPG' || targetFormat.toUpperCase() === 'JPEG' ? DEFAULT_JPEG_QUALITY : undefined

    const convertedData = await convertImageWithMagick(
      fileArrayBuffer,
      magickFormat,
      quality
    )

    // 验证转换数据
    if (!convertedData || convertedData.length === 0) {
      throw new Error('ImageMagick 转换返回空数据')
    }

    console.log('✅ ImageMagick 转换完成，数据长度:', convertedData.length)

    // 验证WebP文件头（如果是WebP格式）
    if (targetFormat.toUpperCase() === 'WEBP') {
      const header = new Uint8Array(convertedData.slice(0, 12))
      const riffSignature = String.fromCharCode(...header.slice(0, 4))
      const webpSignature = String.fromCharCode(...header.slice(8, 12))

      console.log('🔍 WebP 文件头检查:')
      console.log('  原始字节数据:', Array.from(header.slice(0, 4)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
      console.log('  RIFF 签名:', JSON.stringify(riffSignature), '(应该是 "RIFF")')
      console.log('  WEBP 字节数据:', Array.from(header.slice(8, 12)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
      console.log('  WEBP 签名:', JSON.stringify(webpSignature), '(应该是 "WEBP")')

      if (riffSignature !== 'RIFF' || webpSignature !== 'WEBP') {
        console.error('❌ WebP 文件头无效')
        console.error('  完整的前12字节:', Array.from(header).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
        throw new Error('WebP 文件头无效')
      }
    }

    // 创建 Blob
    console.log('📦 创建 Blob...')
    const mimeType = getMimeType(targetFormat)
    const blob = new Blob([convertedData], { type: mimeType })

    // 生成文件名
    const fileName = generateConvertedFileName(file.name, targetFormat)

    console.log('✅ 文件转换成功！输出大小:', convertedData.length, '文件名:', fileName)
    return { blob, fileName }

  } catch (error) {
    console.error('❌ ImageMagick 转换失败:', error)
    throw error
  }
}



/**
 * 获取 MIME 类型
 */
function getMimeType(format: string): string {
  const formatUpper = format.toUpperCase()
  return MIME_TYPE_MAP[formatUpper] || DEFAULT_MIME_TYPE
}

/**
 * 生成转换后的文件名
 */
function generateConvertedFileName(originalName: string, targetFormat: string): string {
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
  const extension = targetFormat.toLowerCase()
  return `${nameWithoutExt}.${extension}`
}

/**
 * 生成转换后的文件名（带冲突检测）
 */
function generateConvertedFileNameWithConflictResolution(
  originalName: string,
  targetFormat: string,
  usedNames: Set<string>
): string {
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
  const originalExt = originalName.match(/\.([^/.]+)$/)?.[1]?.toLowerCase() || ''
  const targetExt = targetFormat.toLowerCase()

  // 如果原始扩展名和目标扩展名相同，直接返回原文件名
  if (originalExt === targetExt) {
    return originalName
  }

  // 基础文件名：原文件名_原扩展名.目标扩展名
  let baseFileName = `${nameWithoutExt}_${originalExt}.${targetExt}`

  // 如果没有冲突，直接返回
  if (!usedNames.has(baseFileName)) {
    return baseFileName
  }

  // 如果有冲突，添加数字后缀
  let counter = 1
  let finalFileName = `${nameWithoutExt}_${originalExt}_${counter}.${targetExt}`

  while (usedNames.has(finalFileName)) {
    counter++
    finalFileName = `${nameWithoutExt}_${originalExt}_${counter}.${targetExt}`
  }

  return finalFileName
}

/**
 * 触发浏览器下载
 */
export function downloadBlob(blob: Blob, fileName: string): void {
  try {
    console.log('开始下载文件:', fileName, '大小:', blob.size)

    // 在popup环境中，直接使用传统下载方法
    fallbackDownload(blob, fileName)

  } catch (error) {
    console.error('下载失败:', error)
    throw error
  }
}

/**
 * 传统下载方法（回退方案）
 */
function fallbackDownload(blob: Blob, fileName: string): void {
  try {
    console.log('使用传统下载方法:', fileName)
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.style.display = 'none'

    // 确保元素被添加到DOM中
    document.body.appendChild(a)

    // 触发下载
    a.click()

    // 立即清理DOM元素
    document.body.removeChild(a)

    // 延迟清理 Object URL
    setTimeout(() => {
      URL.revokeObjectURL(url)
      console.log('已清理Object URL:', fileName)
    }, URL_CLEANUP_DELAY)

    console.log('传统下载触发成功:', fileName)
  } catch (error) {
    console.error('传统下载方法失败:', error)
    throw error
  }
}

/**
 * 批量转换多个文件
 */
export async function convertMultipleFiles(
  files: FileList | File[],
  targetFormat: string,
  onProgress?: ProgressCallback
): Promise<void> {
  const fileArray = Array.from(files)
  const total = fileArray.length

  for (let i = 0; i < fileArray.length; i++) {
    const file = fileArray[i]

    // 只处理图片文件
    if (!file.type.startsWith('image/')) {
      console.warn('跳过非图片文件:', file.name)
      continue
    }

    try {
      onProgress?.(i + 1, total, file.name)

      console.log(`📋 开始处理第 ${i + 1}/${total} 个文件: ${file.name}`)

      // 在每次转换前添加小延迟，确保前一次转换完全完成
      if (i > 0) {
        console.log('⏳ 等待前一次转换完全完成...')
        await new Promise(resolve => setTimeout(resolve, CONVERSION_DELAY))
      }

      const { blob, fileName } = await convertFileToFormat(file, targetFormat)

      // 验证转换结果
      if (blob.size === 0) {
        throw new Error('转换结果为空文件')
      }

      console.log(`✅ 文件 ${file.name} 转换成功，大小: ${blob.size} bytes`)
      downloadBlob(blob, fileName)

      // 增加延迟确保转换和下载的稳定性
      console.log('⏳ 等待下载完成...')
      await new Promise(resolve => setTimeout(resolve, DOWNLOAD_WAIT_TIME))

    } catch (error) {
      console.error('❌ 转换文件失败:', file.name, error)
      // 继续处理下一个文件，不中断整个流程
    }
  }
}

/**
 * 批量转换多个文件并打包成压缩包
 */
export async function convertMultipleFilesToZip(
  files: FileList | File[],
  targetFormat: string,
  onProgress?: ProgressCallback
): Promise<void> {
  const fileArray = Array.from(files)
  const total = fileArray.length
  const zip = new JSZip()
  const usedFileNames = new Set<string>()

  console.log(`📦 开始批量转换 ${total} 个文件到压缩包`)

  for (let i = 0; i < fileArray.length; i++) {
    const file = fileArray[i]

    // 只处理图片文件
    if (!file.type.startsWith('image/')) {
      console.warn('跳过非图片文件:', file.name)
      continue
    }

    try {
      onProgress?.(i + 1, total, file.name)

      console.log(`📋 开始处理第 ${i + 1}/${total} 个文件: ${file.name}`)

      // 在每次转换前添加小延迟，确保前一次转换完全完成
      if (i > 0) {
        console.log('⏳ 等待前一次转换完全完成...')
        await new Promise(resolve => setTimeout(resolve, CONVERSION_DELAY))
      }

      const { blob } = await convertFileToFormat(file, targetFormat)

      // 验证转换结果
      if (blob.size === 0) {
        throw new Error('转换结果为空文件')
      }

      // 生成带冲突检测的文件名
      const fileName = generateConvertedFileNameWithConflictResolution(
        file.name,
        targetFormat,
        usedFileNames
      )
      usedFileNames.add(fileName)

      console.log(`✅ 文件 ${file.name} 转换成功，大小: ${blob.size} bytes，输出文件名: ${fileName}`)

      // 将转换后的文件添加到压缩包中
      zip.file(fileName, blob)
      console.log(`📦 文件 ${fileName} 已添加到压缩包`)

    } catch (error) {
      console.error('❌ 转换文件失败:', file.name, error)
      // 继续处理下一个文件，不中断整个流程
    }
  }

  // 生成压缩包并下载
  try {
    console.log('📦 开始生成压缩包...')
    const zipBlob = await zip.generateAsync({
      type: "blob",
      compression: "DEFLATE",
      compressionOptions: {
        level: ZIP_COMPRESSION_LEVEL // 中等压缩级别，平衡压缩率和速度
      }
    })

    // 生成压缩包文件名
    const now = new Date()
    const dateStr = now.toISOString().slice(0, 10) // YYYY-MM-DD
    const unixTimestamp = Math.floor(now.getTime() / 1000) // Unix时间戳，精确到秒
    const zipFileName = `converted_images_${targetFormat.toLowerCase()}_${dateStr}T${unixTimestamp}.zip`

    console.log(`✅ 压缩包生成成功，大小: ${zipBlob.size} bytes`)

    // 下载压缩包
    downloadBlob(zipBlob, zipFileName)
    console.log(`📥 压缩包 ${zipFileName} 开始下载`)

  } catch (error) {
    console.error('❌ 生成压缩包失败:', error)
    throw error
  }
}

