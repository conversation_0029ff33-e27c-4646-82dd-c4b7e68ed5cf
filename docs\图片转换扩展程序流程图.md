# 图片转换扩展程序流程图

## 1. 主程序流程图

```mermaid
flowchart TD
    A[用户操作] --> B{操作类型}

    %% Popup 流程
    B -->|Popup 上传| C[选择/拖拽图片文件]
    C --> D[加载用户设置]
    D --> E[获取输出格式]
    E --> F{文件数量}
    F -->|单个文件| G[convertFileToFormat]
    F -->|多个文件| H[convertMultipleFilesToZip]

    %% 右键菜单流程
    B -->|右键菜单| I[右键点击图片]
    I --> J[获取图片URL和格式设置]
    J --> K[发送消息到Content Script]
    K --> L[convertImageToFormat]

    %% 转换核心流程
    G --> M[初始化ImageMagick]
    H --> M
    L --> M
    M --> N[读取图片数据]
    N --> O[ImageMagick转换]
    O --> P[获取MIME类型]
    P --> Q[创建Blob对象]
    Q --> R{转换结果验证}
    R -->|失败| S[抛出错误]
    R -->|成功| T[生成文件名]

    %% 下载流程
    T --> U{下载方式}
    U -->|Popup环境| V[传统DOM下载]
    U -->|Content Script| W[DOM下载方法]
    U -->|Background| X[Chrome Downloads API]

    %% 多文件处理
    H --> Y[创建JSZip对象]
    Y --> Z[批量转换文件]
    Z --> AA[添加到压缩包]
    AA --> BB[生成ZIP文件]
    BB --> V

    %% 设置管理
    CC[用户更改格式] --> DD[保存到Chrome Storage]
    DD --> EE[通知Background更新菜单]
    EE --> FF[更新右键菜单文本]

    %% 常量和配置
    GG[constants/converter.ts] --> HH[MIME_TYPE_MAP]
    GG --> II[OUTPUT_FORMAT_OPTIONS]
    GG --> JJ[DEFAULT_USER_SETTINGS]

    HH --> P
    II --> E
    JJ --> D

    %% 样式定义
    classDef userAction fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef storage fill:#e8f5e8
    classDef download fill:#fce4ec
    classDef constants fill:#f1f8e9

    class A,C,I,CC userAction
    class M,N,O,G,H,L,Y,Z,AA,BB process
    class B,F,R,U decision
    class D,DD,EE,FF storage
    class V,W,X download
    class GG,HH,II,JJ constants
```

## 2. 扩展架构组件关系图

```mermaid
graph TB
    subgraph "用户界面层"
        A[popup.tsx<br/>弹出窗口界面]
        B[右键菜单<br/>Context Menu]
    end

    subgraph "后台服务层"
        C[background.ts<br/>后台脚本]
        D[image-converter.ts<br/>Content Script]
    end

    subgraph "工具函数层"
        E[popup-converter.ts<br/>Popup转换工具]
        F[magick-loader.ts<br/>ImageMagick加载器]
        G[storage.ts<br/>存储管理]
    end

    subgraph "配置层"
        H[constants/converter.ts<br/>常量配置]
        I[types/converter.ts<br/>类型定义]
        J[i18n/index.ts<br/>国际化]
    end

    subgraph "外部依赖"
        K["ImageMagick WASM<br/>图片转换引擎"]
        L[JSZip<br/>压缩工具]
        M[Chrome Storage API<br/>数据存储]
        N[Chrome Downloads API<br/>文件下载]
    end

    %% 用户界面层连接
    A --> E
    A --> G
    A --> J
    B --> C

    %% 后台服务层连接
    C --> D
    C --> G
    C --> N
    D --> F
    D --> H

    %% 工具函数层连接
    E --> F
    E --> L
    E --> H
    F --> K
    G --> M

    %% 配置层连接
    H --> I
    J --> A
    J --> C

    %% 数据流向
    A -.->|消息传递| C
    C -.->|消息传递| D
    E -.->|文件转换| F
    F -.->|WASM调用| K

    %% 样式定义
    classDef ui fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef service fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef utils fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef config fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class A,B ui
    class C,D service
    class E,F,G utils
    class H,I,J config
    class K,L,M,N external
```

## 3. MIME类型处理流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Popup as popup.tsx
    participant Converter as popup-converter.ts
    participant Constants as constants/converter.ts
    participant Magick as magick-loader.ts
    participant Browser as 浏览器下载

    User->>Popup: 选择图片文件
    Popup->>Popup: 获取用户设置的输出格式
    Note over Popup: 例如: "JPG"

    Popup->>Converter: convertFileToFormat(file, "JPG")
    Converter->>Magick: 初始化ImageMagick
    Converter->>Magick: convertImageWithMagick(data, format)
    Magick-->>Converter: 返回转换后的二进制数据

    Converter->>Constants: getMimeType("JPG")
    Constants-->>Converter: 返回 "image/jpeg"
    Note over Constants: MIME_TYPE_MAP["JPG"] = "image/jpeg"

    Converter->>Converter: 创建Blob对象
    Note over Converter: new Blob([data], {type: "image/jpeg"})

    Converter->>Converter: 生成文件名
    Note over Converter: "image.jpg"

    Converter->>Browser: 触发下载
    Note over Browser: 浏览器识别MIME类型<br/>正确处理文件下载

    Browser-->>User: 下载完成的图片文件

    rect rgb(255, 245, 238)
        Note over Constants: MIME类型映射表
        Note over Constants: JPG → image/jpeg<br/>PNG → image/png<br/>WEBP → image/webp<br/>GIF → image/gif<br/>等等...
    end
```

## 📊 流程图说明

### 1. **主程序流程图**
展示了从用户操作到文件下载的完整流程，包括：
- **两种入口**：Popup 上传和右键菜单
- **核心转换流程**：ImageMagick 初始化、转换、MIME 类型处理
- **多种下载方式**：DOM 下载、Chrome Downloads API
- **多文件处理**：ZIP 压缩功能
- **设置管理**：格式配置和存储

### 2. **架构组件关系图**
展示了扩展的分层架构：
- **用户界面层**：popup.tsx 和右键菜单
- **后台服务层**：background.ts 和 content script
- **工具函数层**：转换工具、存储管理
- **配置层**：常量、类型定义、国际化
- **外部依赖**：ImageMagick、JSZip、Chrome APIs

### 3. **MIME 类型处理流程图**
详细展示了 MIME 类型在转换过程中的关键作用：
- 从用户选择格式到获取对应的 MIME 类型
- MIME 类型如何用于创建正确的 Blob 对象
- 浏览器如何利用 MIME 类型正确处理下载

## 🎯 关键流程要点

1. **统一的配置管理**：所有 MIME 类型映射集中在 `constants/converter.ts`
2. **类型安全**：使用 TypeScript 确保类型正确性
3. **多路径支持**：支持 Popup 和右键菜单两种使用方式
4. **错误处理**：包含完整的错误处理和回退机制
5. **国际化支持**：UI 文本根据浏览器语言自动切换

## 📋 MIME 类型的重要作用

### 1. **浏览器文件识别**
- 告诉浏览器这是什么类型的文件
- 影响浏览器如何处理和显示文件

### 2. **下载行为控制**
- 正确的 MIME 类型确保文件以正确的格式下载
- 影响文件的默认打开方式

### 3. **Data URL 生成**
- 在创建 Data URL 时，MIME 类型决定了 URL 的格式
- 例如：`data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...`

### 4. **文件验证**
- 帮助系统验证文件的真实类型
- 防止文件类型欺骗