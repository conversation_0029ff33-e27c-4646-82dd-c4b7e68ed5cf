{"name": "avif-to-jpg", "displayName": "__MSG_extensionName__", "version": "1.0.0", "description": "__MSG_extensionDescription__", "author": "BetterAI <<EMAIL>>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@imagemagick/magick-wasm": "^0.0.35", "i18next": "^25.2.1", "jszip": "^3.10.1", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.5.2"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/jszip": "^3.4.1", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "prettier": "3.2.4", "tailwindcss": "^3.4.17", "typescript": "5.3.3"}, "manifest": {"default_locale": "en", "host_permissions": ["https://*/*"], "permissions": ["tabs", "contextMenus", "downloads", "storage"], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["node_modules/@imagemagick/magick-wasm/dist/magick.wasm"], "matches": ["<all_urls>"]}]}}